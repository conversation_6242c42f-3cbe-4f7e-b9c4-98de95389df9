// Localized route configuration
export const routes = {
  home: {
    bs: '/',
    en: '/en',
    de: '/de'
  },
  about: {
    bs: '/o-nama',
    en: '/en/about',
    de: '/de/uber-uns'
  },
  services: {
    bs: '/services',
    en: '/en/services',
    de: '/de/services'
  },
  contact: {
    bs: '/contact',
    en: '/en/contact',
    de: '/de/contact'
  }
} as const;

// Helper function to get localized route
export function getLocalizedRoute(
  route: keyof typeof routes,
  locale: 'bs' | 'en' | 'de'
): string {
  return routes[route][locale];
}

// Helper function to get current page from pathname
export function getCurrentPage(pathname: string): keyof typeof routes | null {
  // Remove trailing slash
  const cleanPath = pathname.replace(/\/$/, '') || '/';
  
  // Check each route
  for (const [page, locales] of Object.entries(routes)) {
    for (const [locale, path] of Object.entries(locales)) {
      if (cleanPath === path || cleanPath === path.replace(/\/$/, '')) {
        return page as keyof typeof routes;
      }
    }
  }
  
  return null;
}

// Helper function to get current locale from pathname
export function getCurrentLocale(pathname: string): 'bs' | 'en' | 'de' {
  if (pathname.startsWith('/en')) return 'en';
  if (pathname.startsWith('/de')) return 'de';
  return 'bs';
}

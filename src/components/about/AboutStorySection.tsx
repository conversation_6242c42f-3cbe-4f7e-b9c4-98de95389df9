'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';

const AboutStorySection = () => {
  const t = useTranslations('about.story');

  // Intersection observers for staggered animations
  const { ref: cornerTextRef, inView: cornerTextInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: contentRef, inView: contentInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: missionRef, inView: missionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="py-32 relative overflow-hidden">
      {/* Background with subtle pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-albatros-ivory via-white to-albatros-ivory"></div>
      <div className="grain !opacity-5"></div>

      <div className="relative mx-auto px-6 lg:px-8">
        <div className="flex">
        {/* Large centered title */}
        <div
          ref={titleRef}
          className={`mb-20 max-w-2/3 transition-all duration-1000 ease-out delay-300 ${
            titleInView
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
          }`}
        >
          <h2 className="text-5xl lg:text-6xl font-normal text-black leading-tight max-w-5xl mx-auto">
            {t('title')}
          </h2>
        </div>

        {/* Small subtitle */}
        <div class="md:w-1/3"><p class="text-sm text-albatros-ivory font-light"><span class="font-bold">Albatros</span><br>Unsere Werte</p></div>
        <div
          ref={cornerTextRef}
          className={`mb-16 transition-all duration-1000 ease-out delay-100 ${
            cornerTextInView
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
          }`}
        >
          <p className="text-sm text-black/60 font-light uppercase tracking-wider">
            <span className="font-bold">Albatros</span> • {t('subtitle')}
          </p>
        </div>
        </div>

        {/* Content in cards/blocks */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {/* Story content */}
          <div
            ref={contentRef}
            className={`transition-all duration-1000 ease-out delay-500 ${
              contentInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-white/50 backdrop-blur-sm rounded-2xl p-8 h-full border border-black/5">
              <div className="text-4xl mb-6 text-black/20">01</div>
              <h3 className="text-xl font-semibold text-black mb-4">Naša priča</h3>
              <p className="text-black/70 leading-relaxed">
                {t('content')}
              </p>
            </div>
          </div>

          {/* Mission statement */}
          <div
            ref={missionRef}
            className={`transition-all duration-1000 ease-out delay-700 ${
              missionInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <div className="bg-black/5 backdrop-blur-sm rounded-2xl p-8 h-full border border-black/10">
              <div className="text-4xl mb-6 text-black/20">02</div>
              <h3 className="text-xl font-semibold text-black mb-4">Naša misija</h3>
              <p className="text-black/70 leading-relaxed font-medium">
                {t('mission')}
              </p>
            </div>
          </div>
        </div>

        {/* Bottom decorative element */}
        <div className="w-24 h-px bg-black/10 mx-auto"></div>
      </div>
    </section>
  );
};

export default AboutStorySection;

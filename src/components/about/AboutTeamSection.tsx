'use client';

import { useTranslations } from 'next-intl';
import { useInView } from 'react-intersection-observer';
import Image from 'next/image';

const AboutTeamSection = () => {
  const t = useTranslations('about.team');

  // Intersection observers for staggered animations
  const { ref: imageRef, inView: imageInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: descriptionRef, inView: descriptionInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: commitmentRef, inView: commitmentInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section className="min-h-screen flex">
      {/* Left Half - Content */}
      <div className="w-1/2 bg-albatros-ivory relative flex items-center justify-center p-12">
        <div className="grain"></div>
        <div className="max-w-2xl h-full max-h-[600px] flex flex-col justify-between relative">
          {/* Small subtitle */}
          <div className="mb-8">
            <p className="text-sm text-black/60 font-light">
              <span className="font-bold">Albatros</span><br />
              {t('subtitle')}
            </p>
          </div>

          {/* Main Title */}
          <div
            ref={titleRef}
            className={`flex flex-col transition-all duration-1000 ease-out delay-300 ${
              titleInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <h2 className="text-4xl lg:text-5xl mb-8 font-bold text-albatros-black leading-tight">
              {t('title')}
            </h2>
          </div>

          {/* Description */}
          <div
            ref={descriptionRef}
            className={`mb-8 transition-all duration-1000 ease-out delay-500 ${
              descriptionInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <p className="text-xl text-black/80 leading-relaxed">
              {t('description')}
            </p>
          </div>

          {/* Commitment Text */}
          <div
            ref={commitmentRef}
            className={`pt-8 border-t border-albatros-black/10 transition-all duration-1000 ease-out delay-700 ${
              commitmentInView
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-8'
            }`}
          >
            <p className="text-sm text-black font-medium leading-tight">
              {t('commitment')}
            </p>
          </div>
        </div>
      </div>

      {/* Right Half - Image */}
      <div
        ref={imageRef}
        className={`w-1/2 relative overflow-hidden transition-all duration-1000 ease-out delay-100 ${
          imageInView
            ? 'opacity-100 scale-100'
            : 'opacity-0 scale-105'
        }`}
      >
        <Image
          src="/images/about-building.jpg"
          alt="AlbatrosDoc Team"
          fill
          className="object-cover"
          sizes="50vw"
        />
        {/* Optional overlay for better contrast */}
        <div className="absolute inset-0 bg-black/20 z-10"></div>
      </div>
    </section>
  );
};

export default AboutTeamSection;

/* Features Section - Dark Gradient Animation */

@keyframes features-gradient-animation {
    0% {
        --c-0: hsla(220.2352456485527, 25%, 25%, 1);
        --x-0: 7%;
        --y-0: 91%;
        --s-start-0: 16%;
        --s-end-0: 81%;
        --c-1: hsla(249, 97%, 3%, 1);
        --x-1: 92%;
        --y-1: 41%;
        --s-start-1: 16%;
        --s-end-1: 81%;
        --c-2: hsla(348.1709145427288, 2%, 34%, 1);
        --x-2: 16%;
        --y-2: 94%;
        --s-start-2: 16%;
        --s-end-2: 81%;
        --c-3: hsla(200.38230447208187, 71%, 34%, 1);
        --x-3: 49%;
        --y-3: 8%;
        --s-start-3: 16%;
        --s-end-3: 81%;
        --c-4: hsla(257, 94%, 17%, 1);
        --x-4: 82%;
        --y-4: 58%;
        --s-start-4: 16%;
        --s-end-4: 81%;
    }

    50% {
        --c-0: hsla(344, 52%, 9%, 1);
        --x-0: 58%;
        --y-0: 74%;
        --s-start-0: 16%;
        --s-end-0: 81%;
        --c-1: hsla(199.0587750603171, 61%, 27%, 1);
        --x-1: 98%;
        --y-1: 79%;
        --s-start-1: 16%;
        --s-end-1: 81%;
        --c-2: hsla(232.14701035443477, 80%, 11%, 1);
        --x-2: 36%;
        --y-2: 28%;
        --s-start-2: 16%;
        --s-end-2: 81%;
        --c-3: hsla(266, 22%, 28%, 1);
        --x-3: 49%;
        --y-3: 10%;
        --s-start-3: 16%;
        --s-end-3: 81%;
        --c-4: hsla(326, 21%, 11%, 1);
        --x-4: 64%;
        --y-4: 63%;
        --s-start-4: 16%;
        --s-end-4: 81%;
    }

    100% {
        --c-0: hsla(188.47053976619964, 20%, 22%, 1);
        --x-0: 14%;
        --y-0: 6%;
        --s-start-0: 16%;
        --s-end-0: 81%;
        --c-1: hsla(266.9999999999997, 7%, 23%, 1);
        --x-1: 6%;
        --y-1: 70%;
        --s-start-1: 16%;
        --s-end-1: 81%;
        --c-2: hsla(263.9117162367876, 87%, 16%, 1);
        --x-2: 91%;
        --y-2: 12%;
        --s-start-2: 16%;
        --s-end-2: 81%;
        --c-3: hsla(359, 38%, 5%, 1);
        --x-3: 87%;
        --y-3: 50%;
        --s-start-3: 16%;
        --s-end-3: 81%;
        --c-4: hsla(227, 78%, 15%, 1);
        --x-4: 74%;
        --y-4: 14%;
        --s-start-4: 16%;
        --s-end-4: 81%;
    }
}

@property --c-0 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(220.2352456485527, 25%, 25%, 1)
}

@property --x-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 7%
}

@property --y-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 91%
}

@property --s-start-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --s-end-0 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

@property --c-1 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(249, 97%, 3%, 1)
}

@property --x-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 92%
}

@property --y-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 41%
}

@property --s-start-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --s-end-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

@property --c-2 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(348.1709145427288, 2%, 34%, 1)
}

@property --x-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --y-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 94%
}

@property --s-start-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --s-end-2 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

@property --c-3 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(200.38230447208187, 71%, 34%, 1)
}

@property --x-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 49%
}

@property --y-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 8%
}

@property --s-start-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --s-end-3 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

@property --c-4 {
    syntax: '<color>';
    inherits: false;
    initial-value: hsla(257, 94%, 17%, 1)
}

@property --x-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 82%
}

@property --y-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 58%
}

@property --s-start-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 16%
}

@property --s-end-4 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 81%
}

.features-gradient-bg {
    --c-0: hsla(220.2352456485527, 25%, 25%, 1);
    --x-0: 7%;
    --y-0: 91%;
    --c-1: hsla(249, 97%, 3%, 1);
    --x-1: 92%;
    --y-1: 41%;
    --c-2: hsla(348.1709145427288, 2%, 34%, 1);
    --x-2: 16%;
    --y-2: 94%;
    --c-3: hsla(200.38230447208187, 71%, 34%, 1);
    --x-3: 49%;
    --y-3: 8%;
    --c-4: hsla(257, 94%, 17%, 1);
    --x-4: 82%;
    --y-4: 58%;
    background-color: hsla(248, 28%, 23%, 1);
    background-image: radial-gradient(circle at var(--x-0) var(--y-0), var(--c-0) var(--s-start-0), transparent var(--s-end-0)), radial-gradient(circle at var(--x-1) var(--y-1), var(--c-1) var(--s-start-1), transparent var(--s-end-1)), radial-gradient(circle at var(--x-2) var(--y-2), var(--c-2) var(--s-start-2), transparent var(--s-end-2)), radial-gradient(circle at var(--x-3) var(--y-3), var(--c-3) var(--s-start-3), transparent var(--s-end-3)), radial-gradient(circle at var(--x-4) var(--y-4), var(--c-4) var(--s-start-4), transparent var(--s-end-4));
    animation: features-gradient-animation 3s ease-in-out infinite alternate;
    background-blend-mode: normal, normal, normal, normal, normal;
}
